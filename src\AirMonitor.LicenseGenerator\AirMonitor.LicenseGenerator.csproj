<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Resources\Images\license-generator.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Syncfusion.SfBusyIndicator.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.Shared.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.Themes.Windows11Dark.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.Themes.Windows11Light.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.Tools.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.SfGrid.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.SfSkinManager.WPF" Version="29.2.9" />
    <PackageReference Include="Syncfusion.SfTextInputLayout.WPF" Version="29.2.9" />
    <PackageReference Include="System.Management" Version="9.0.5" />
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AirMonitor.Core\AirMonitor.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\Images\license-generator.ico" />
  </ItemGroup>

</Project>
