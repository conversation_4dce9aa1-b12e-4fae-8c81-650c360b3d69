using Microsoft.Extensions.Logging;
using AirMonitor.Core.Models;
using AirMonitor.Core.Interfaces;
using AirMonitor.Core.Utilities;
using System.Text.Json;

namespace AirMonitor.LicenseGenerator.Services;

/// <summary>
/// License生成服务实现
/// 负责生成加密的许可证文件
/// </summary>
public class LicenseGeneratorService : ILicenseGeneratorService
{
    private readonly ILogger<LicenseGeneratorService> _logger;
    private readonly ICryptoService _cryptoService;

    public LicenseGeneratorService(
        ILogger<LicenseGeneratorService> logger,
        ICryptoService cryptoService)
    {
        _logger = logger;
        _cryptoService = cryptoService;
    }

    public string GenerateLicense(LicenseInfo licenseInfo)
    {
        try
        {
            _logger.LogInformation("开始生成许可证: {LicenseId}", licenseInfo.LicenseId);

            // 1. 验证许可证信息
            ValidateLicenseInfo(licenseInfo);

            // 2. 序列化许可证信息
            var licenseJson = JsonHelper.Serialize(licenseInfo);
            _logger.LogDebug("许可证信息序列化完成");

            // 3. 加密许可证内容
            var encryptedContent = _cryptoService.Encrypt(licenseJson);
            _logger.LogDebug("许可证内容加密完成");

            // 4. 生成数字签名
            var signature = _cryptoService.GenerateSignature(licenseJson);
            _logger.LogDebug("数字签名生成完成");

            // 5. 创建最终的许可证文件内容
            var licenseFile = new
            {
                Version = "1.0",
                Content = encryptedContent,
                Signature = signature,
                GeneratedAt = DateTime.UtcNow,
                Generator = "AirMonitor License Generator v1.0"
            };

            var finalContent = JsonHelper.Serialize(licenseFile, true);
            
            _logger.LogInformation("许可证生成成功: {LicenseId}, 大小: {Size} bytes", 
                licenseInfo.LicenseId, finalContent.Length);

            return finalContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成许可证失败: {LicenseId}", licenseInfo?.LicenseId);
            throw;
        }
    }

    public async Task<string> GenerateLicenseAsync(LicenseInfo licenseInfo)
    {
        return await Task.Run(() => GenerateLicense(licenseInfo));
    }

    public List<string> GenerateBatchLicenses(List<LicenseInfo> licenseInfos)
    {
        try
        {
            _logger.LogInformation("开始批量生成许可证，数量: {Count}", licenseInfos.Count);

            var results = new List<string>();
            var errors = new List<string>();

            for (int i = 0; i < licenseInfos.Count; i++)
            {
                try
                {
                    var license = GenerateLicense(licenseInfos[i]);
                    results.Add(license);
                    _logger.LogDebug("批量生成进度: {Current}/{Total}", i + 1, licenseInfos.Count);
                }
                catch (Exception ex)
                {
                    var error = $"第 {i + 1} 个许可证生成失败: {ex.Message}";
                    errors.Add(error);
                    results.Add(string.Empty); // 占位符，保持索引对应
                    _logger.LogWarning(ex, "批量生成中的许可证失败: {Index}, {LicenseId}", 
                        i + 1, licenseInfos[i]?.LicenseId);
                }
            }

            if (errors.Any())
            {
                var errorMessage = string.Join(Environment.NewLine, errors);
                _logger.LogWarning("批量生成完成，但有错误: {ErrorCount}/{Total}", errors.Count, licenseInfos.Count);
                throw new InvalidOperationException($"批量生成部分失败:\n{errorMessage}");
            }

            _logger.LogInformation("批量生成许可证成功，数量: {Count}", results.Count);
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量生成许可证失败");
            throw;
        }
    }

    public async Task<List<string>> GenerateBatchLicensesAsync(List<LicenseInfo> licenseInfos)
    {
        return await Task.Run(() => GenerateBatchLicenses(licenseInfos));
    }

    public bool ValidateLicenseInfo(LicenseInfo licenseInfo)
    {
        try
        {
            if (licenseInfo == null)
                throw new ArgumentNullException(nameof(licenseInfo), "许可证信息不能为空");

            if (string.IsNullOrWhiteSpace(licenseInfo.LicenseId))
                throw new ArgumentException("许可证ID不能为空", nameof(licenseInfo.LicenseId));

            if (string.IsNullOrWhiteSpace(licenseInfo.ProductName))
                throw new ArgumentException("产品名称不能为空", nameof(licenseInfo.ProductName));

            if (string.IsNullOrWhiteSpace(licenseInfo.ProductVersion))
                throw new ArgumentException("产品版本不能为空", nameof(licenseInfo.ProductVersion));

            if (string.IsNullOrWhiteSpace(licenseInfo.DepartmentName))
                throw new ArgumentException("部门名称不能为空", nameof(licenseInfo.DepartmentName));

            if (string.IsNullOrWhiteSpace(licenseInfo.DepartmentEmail))
                throw new ArgumentException("部门邮箱不能为空", nameof(licenseInfo.DepartmentEmail));

            if (string.IsNullOrWhiteSpace(licenseInfo.HardwareFingerprint))
                throw new ArgumentException("硬件指纹不能为空", nameof(licenseInfo.HardwareFingerprint));

            if (licenseInfo.ValidFrom >= licenseInfo.ValidTo && licenseInfo.ValidTo != DateTime.MaxValue)
                throw new ArgumentException("有效期开始时间必须早于结束时间");

            if (licenseInfo.AuthorizedFeatures == null || !licenseInfo.AuthorizedFeatures.Any())
                throw new ArgumentException("必须至少授权一个功能", nameof(licenseInfo.AuthorizedFeatures));

            _logger.LogDebug("许可证信息验证通过: {LicenseId}", licenseInfo.LicenseId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "许可证信息验证失败: {LicenseId}", licenseInfo?.LicenseId);
            throw;
        }
    }

    public LicenseInfo CreateLicenseFromTemplate(LicenseTemplate template, string departmentName, string departmentEmail, string hardwareFingerprint)
    {
        try
        {
            if (template == null)
                throw new ArgumentNullException(nameof(template), "模板不能为空");

            var licenseInfo = new LicenseInfo
            {
                LicenseId = Guid.NewGuid().ToString("N")[..8].ToUpper(),
                ProductName = template.ProductName,
                ProductVersion = template.ProductVersion,
                LicenseType = template.LicenseType,
                DepartmentName = departmentName,
                DepartmentEmail = departmentEmail,
                ValidFrom = DateTime.Now,
                ValidTo = template.ValidityDays > 0 ? DateTime.Now.AddDays(template.ValidityDays) : DateTime.MaxValue,
                HardwareFingerprint = hardwareFingerprint,
                AuthorizedFeatures = template.AuthorizedFeatures?.ToList() ?? new List<Core.Enums.FeatureCode>(),
                Notes = template.Description,
                GeneratedAt = DateTime.Now,
                GeneratedBy = Environment.UserName
            };

            _logger.LogDebug("从模板创建许可证信息: {TemplateName} -> {LicenseId}", template.Name, licenseInfo.LicenseId);
            return licenseInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从模板创建许可证信息失败: {TemplateName}", template?.Name);
            throw;
        }
    }

    public string GenerateLicenseId()
    {
        var licenseId = Guid.NewGuid().ToString("N")[..8].ToUpper();
        _logger.LogDebug("生成新的许可证ID: {LicenseId}", licenseId);
        return licenseId;
    }

    public void SaveLicenseToFile(string licenseContent, string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(licenseContent))
                throw new ArgumentException("许可证内容不能为空", nameof(licenseContent));

            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            File.WriteAllText(filePath, licenseContent);
            _logger.LogInformation("许可证文件已保存: {FilePath}, 大小: {Size} bytes", filePath, licenseContent.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存许可证文件失败: {FilePath}", filePath);
            throw;
        }
    }

    public async Task SaveLicenseToFileAsync(string licenseContent, string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(licenseContent))
                throw new ArgumentException("许可证内容不能为空", nameof(licenseContent));

            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            await File.WriteAllTextAsync(filePath, licenseContent);
            _logger.LogInformation("许可证文件已保存: {FilePath}, 大小: {Size} bytes", filePath, licenseContent.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存许可证文件失败: {FilePath}", filePath);
            throw;
        }
    }
}
