<UserControl x:Class="AirMonitor.LicenseGenerator.Views.LicenseValidatorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
             mc:Ignorable="d" 
             FontFamily="Microsoft YaHei"
             FontSize="14"
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
        
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
        
        <Style x:Key="SectionBorderStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="#E1E4E8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Background" Value="White"/>
        </Style>
        
        <Style x:Key="ValidStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#28A745"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        
        <Style x:Key="InvalidStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#DC3545"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
        <StackPanel>
            <!-- 文件选择区域 -->
            <Border Style="{StaticResource SectionBorderStyle}">
                <StackPanel>
                    <TextBlock Text="选择许可证文件" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="文件路径:" Style="{StaticResource LabelStyle}"/>
                        <TextBox Grid.Column="1" Text="{Binding SelectedFilePath}" 
                                 IsReadOnly="True" Background="#F6F8FA"
                                 Padding="8" BorderThickness="1" BorderBrush="#D0D7DE" 
                                 VerticalAlignment="Center" Margin="0,5"/>
                        <Button Grid.Column="2" Content="浏览..." Command="{Binding SelectLicenseFileCommand}" 
                                Padding="15,8" Margin="10,5,0,5"/>
                    </Grid>
                    
                    <!-- 拖拽提示 -->
                    <TextBlock Text="提示：您也可以直接将许可证文件拖拽到此区域" 
                               FontSize="12" Foreground="Gray" 
                               HorizontalAlignment="Center" Margin="0,10,0,0"/>
                </StackPanel>
            </Border>

            <!-- 验证操作区域 -->
            <Border Style="{StaticResource SectionBorderStyle}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Content="验证许可证" Command="{Binding ValidateLicenseCommand}" 
                                Padding="20,10" Margin="0,0,15,0" FontWeight="SemiBold"
                                Background="#0078D4" Foreground="White" BorderThickness="0"/>
                        <Button Content="清空结果" Command="{Binding ClearValidationResultCommand}" 
                                Padding="20,10" Margin="0,0,15,0"/>
                        <Button Content="导出报告" Command="{Binding ExportValidationReportCommand}" 
                                Padding="20,10" IsEnabled="{Binding IsValidLicense}"/>
                    </StackPanel>
                    
                    <!-- 加载指示器 -->
                    <syncfusion:SfBusyIndicator 
                        IsBusy="{Binding IsValidating}"
                        AnimationType="DoubleCircle"
                        Width="32" Height="32"
                        HorizontalAlignment="Center"
                        Margin="0,15,0,0"/>
                </StackPanel>
            </Border>

            <!-- 验证状态区域 -->
            <Border Style="{StaticResource SectionBorderStyle}"
                    Visibility="{Binding ValidationStatus, Converter={StaticResource StringToVisibilityConverter}}">
                <StackPanel>
                    <TextBlock Text="验证状态" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <Ellipse Width="16" Height="16" VerticalAlignment="Center" Margin="0,0,10,0">
                            <Ellipse.Fill>
                                <SolidColorBrush Color="{Binding IsValidLicense, Converter={StaticResource BooleanToColorConverter}, ConverterParameter='#28A745,#DC3545'}"/>
                            </Ellipse.Fill>
                        </Ellipse>
                        <TextBlock Text="{Binding ValidationStatus}" VerticalAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource {x:Type TextBlock}}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsValidLicense}" Value="True">
                                            <Setter Property="Foreground" Value="#28A745"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding IsValidLicense}" Value="False">
                                            <Setter Property="Foreground" Value="#DC3545"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- 许可证详细信息区域 -->
            <Border Style="{StaticResource SectionBorderStyle}"
                    Visibility="{Binding LicenseInfo, Converter={StaticResource NullToVisibilityConverter}}">
                <StackPanel>
                    <TextBlock Text="许可证详细信息" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <!-- 使用DataGrid显示许可证属性 -->
                    <syncfusion:SfDataGrid 
                        ItemsSource="{Binding LicenseProperties}"
                        AutoGenerateColumns="False"
                        HeaderRowHeight="35"
                        RowHeight="30"
                        GridLinesVisibility="Horizontal"
                        HeaderStyle="{StaticResource HeaderStyle}"
                        Margin="0,10">
                        
                        <syncfusion:SfDataGrid.Columns>
                            <syncfusion:GridTextColumn 
                                HeaderText="属性名称" 
                                MappingName="Name" 
                                Width="150"
                                TextAlignment="Left"/>
                            <syncfusion:GridTextColumn 
                                HeaderText="属性值" 
                                MappingName="Value" 
                                Width="*"
                                TextAlignment="Left"
                                TextWrapping="Wrap"/>
                        </syncfusion:SfDataGrid.Columns>
                    </syncfusion:SfDataGrid>
                </StackPanel>
            </Border>

            <!-- 错误信息区域 -->
            <Border Style="{StaticResource SectionBorderStyle}"
                    Background="#FFF5F5"
                    BorderBrush="#FEB2B2"
                    Visibility="{Binding ValidationResult.ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <Path Data="M12,2C13.1,2 14,2.9 14,4C14,5.1 13.1,6 12,6C10.9,6 10,5.1 10,4C10,2.9 10.9,2 12,2M21,9V7L15,1H5C3.89,1 3,1.89 3,3V21A2,2 0 0,0 5,23H19A2,2 0 0,0 21,21V9M19,9H14V4H5V21H19V9Z" 
                              Fill="#DC3545" Width="16" Height="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="验证错误" FontWeight="SemiBold" Foreground="#DC3545" FontSize="14"/>
                    </StackPanel>
                    
                    <Border Background="#FED7D7" CornerRadius="4" Padding="10">
                        <TextBlock Text="{Binding ValidationResult.ErrorMessage}"
                                   TextWrapping="Wrap" Foreground="#721C24"/>
                    </Border>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
