using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using AirMonitor.Core.Models;
using AirMonitor.Core.Interfaces;
using Microsoft.Win32;
using System.Collections.ObjectModel;

namespace AirMonitor.LicenseGenerator.ViewModels;

/// <summary>
/// License验证器ViewModel
/// 负责许可证验证相关的界面逻辑
/// </summary>
public partial class LicenseValidatorViewModel : ObservableObject
{
    private readonly ILogger<LicenseValidatorViewModel> _logger;
    private readonly ILicenseService _licenseService;

    [ObservableProperty]
    private string selectedFilePath = "";

    [ObservableProperty]
    private bool isValidating = false;

    [ObservableProperty]
    private string validationStatus = "";

    [ObservableProperty]
    private bool isValidLicense = false;

    [ObservableProperty]
    private LicenseInfo? licenseInfo;

    [ObservableProperty]
    private ValidationResult? validationResult;

    public ObservableCollection<LicenseProperty> LicenseProperties { get; }

    public LicenseValidatorViewModel(
        ILogger<LicenseValidatorViewModel> logger,
        ILicenseService licenseService)
    {
        _logger = logger;
        _licenseService = licenseService;
        LicenseProperties = new ObservableCollection<LicenseProperty>();

        _logger.LogInformation("License验证器ViewModel已初始化");
    }

    [RelayCommand]
    private void SelectLicenseFile()
    {
        try
        {
            var openDialog = new OpenFileDialog
            {
                Title = "选择许可证文件",
                Filter = "License文件 (*.lic)|*.lic|所有文件 (*.*)|*.*",
                Multiselect = false
            };

            if (openDialog.ShowDialog() == true)
            {
                SelectedFilePath = openDialog.FileName;
                ValidationStatus = $"已选择文件: {Path.GetFileName(SelectedFilePath)}";
                _logger.LogDebug("选择许可证文件: {FilePath}", SelectedFilePath);
            }
        }
        catch (Exception ex)
        {
            ValidationStatus = $"选择文件时发生错误: {ex.Message}";
            _logger.LogError(ex, "选择许可证文件时发生错误");
        }
    }

    [RelayCommand]
    private async Task ValidateLicenseAsync()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(SelectedFilePath) || !File.Exists(SelectedFilePath))
            {
                ValidationStatus = "请先选择有效的许可证文件";
                return;
            }

            ValidationStatus = "正在验证许可证...";
            IsValidating = true;
            ClearValidationResult();

            var result = await Task.Run(() => _licenseService.ValidateLicense(SelectedFilePath));
            ValidationResult = result;

            if (result.IsValid)
            {
                IsValidLicense = true;
                LicenseInfo = result.LicenseInfo;
                ValidationStatus = "许可证验证成功";
                UpdateLicenseProperties();
                _logger.LogInformation("许可证验证成功: {LicenseId}", result.LicenseInfo?.LicenseId);
            }
            else
            {
                IsValidLicense = false;
                ValidationStatus = $"许可证验证失败: {result.ErrorMessage}";
                _logger.LogWarning("许可证验证失败: {Error}", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            IsValidLicense = false;
            ValidationStatus = $"验证过程中发生错误: {ex.Message}";
            _logger.LogError(ex, "验证许可证时发生错误");
        }
        finally
        {
            IsValidating = false;
        }
    }

    private void UpdateLicenseProperties()
    {
        LicenseProperties.Clear();

        if (LicenseInfo == null) return;

        LicenseProperties.Add(new LicenseProperty("许可证ID", LicenseInfo.LicenseId));
        LicenseProperties.Add(new LicenseProperty("产品名称", LicenseInfo.ProductName));
        LicenseProperties.Add(new LicenseProperty("产品版本", LicenseInfo.ProductVersion));
        LicenseProperties.Add(new LicenseProperty("许可证类型", GetLicenseTypeDisplayName(LicenseInfo.LicenseType)));
        LicenseProperties.Add(new LicenseProperty("部门名称", LicenseInfo.DepartmentName));
        LicenseProperties.Add(new LicenseProperty("部门邮箱", LicenseInfo.DepartmentEmail));
        LicenseProperties.Add(new LicenseProperty("生效时间", LicenseInfo.ValidFrom.ToString("yyyy-MM-dd HH:mm:ss")));
        
        if (LicenseInfo.ValidTo == DateTime.MaxValue)
        {
            LicenseProperties.Add(new LicenseProperty("到期时间", "永久有效"));
        }
        else
        {
            LicenseProperties.Add(new LicenseProperty("到期时间", LicenseInfo.ValidTo.ToString("yyyy-MM-dd HH:mm:ss")));
        }

        LicenseProperties.Add(new LicenseProperty("硬件指纹", LicenseInfo.HardwareFingerprint));
        LicenseProperties.Add(new LicenseProperty("生成时间", LicenseInfo.GeneratedAt.ToString("yyyy-MM-dd HH:mm:ss")));
        LicenseProperties.Add(new LicenseProperty("生成者", LicenseInfo.GeneratedBy));

        if (!string.IsNullOrWhiteSpace(LicenseInfo.Notes))
        {
            LicenseProperties.Add(new LicenseProperty("备注", LicenseInfo.Notes));
        }

        // 添加授权功能列表
        if (LicenseInfo.AuthorizedFeatures?.Any() == true)
        {
            var features = string.Join(", ", LicenseInfo.AuthorizedFeatures.Select(GetFeatureDisplayName));
            LicenseProperties.Add(new LicenseProperty("授权功能", features));
        }

        _logger.LogDebug("已更新许可证属性显示，共 {Count} 项", LicenseProperties.Count);
    }

    private static string GetLicenseTypeDisplayName(Core.Enums.LicenseType licenseType)
    {
        return licenseType switch
        {
            Core.Enums.LicenseType.Standard => "普通版",
            Core.Enums.LicenseType.AfterSales => "售后版",
            Core.Enums.LicenseType.Development => "研发版",
            Core.Enums.LicenseType.Management => "管理版",
            _ => licenseType.ToString()
        };
    }

    private static string GetFeatureDisplayName(Core.Enums.FeatureCode featureCode)
    {
        return featureCode switch
        {
            Core.Enums.FeatureCode.SerialCommunication => "串口通信",
            Core.Enums.FeatureCode.DataCollection => "数据采集",
            Core.Enums.FeatureCode.RealTimeMonitoring => "实时监控",
            Core.Enums.FeatureCode.HistoryDataQuery => "历史数据查询",
            Core.Enums.FeatureCode.DataExport => "数据导出",
            Core.Enums.FeatureCode.AdvancedAnalysis => "高级分析",
            Core.Enums.FeatureCode.DataPlayback => "数据回放",
            Core.Enums.FeatureCode.AlarmManagement => "报警管理",
            Core.Enums.FeatureCode.CustomProtocol => "自定义协议",
            Core.Enums.FeatureCode.SystemConfiguration => "系统配置",
            Core.Enums.FeatureCode.UserManagement => "用户管理",
            Core.Enums.FeatureCode.LicenseManagement => "许可证管理",
            _ => featureCode.ToString()
        };
    }

    [RelayCommand]
    private void ClearValidationResult()
    {
        IsValidLicense = false;
        LicenseInfo = null;
        ValidationResult = null;
        LicenseProperties.Clear();
        
        if (string.IsNullOrWhiteSpace(SelectedFilePath))
        {
            ValidationStatus = "";
        }

        _logger.LogDebug("已清空验证结果");
    }

    [RelayCommand]
    private async Task ExportValidationReportAsync()
    {
        try
        {
            if (ValidationResult == null || LicenseInfo == null)
            {
                ValidationStatus = "没有可导出的验证结果";
                return;
            }

            var saveDialog = new SaveFileDialog
            {
                Title = "导出验证报告",
                Filter = "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*",
                FileName = $"LicenseValidation_{LicenseInfo.LicenseId}_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            };

            if (saveDialog.ShowDialog() == true)
            {
                var report = GenerateValidationReport();
                await File.WriteAllTextAsync(saveDialog.FileName, report);
                ValidationStatus = $"验证报告已导出到: {saveDialog.FileName}";
                _logger.LogInformation("验证报告已导出: {FileName}", saveDialog.FileName);
            }
        }
        catch (Exception ex)
        {
            ValidationStatus = $"导出验证报告失败: {ex.Message}";
            _logger.LogError(ex, "导出验证报告时发生错误");
        }
    }

    private string GenerateValidationReport()
    {
        var report = new System.Text.StringBuilder();
        report.AppendLine("AirMonitor 许可证验证报告");
        report.AppendLine("=" + new string('=', 50));
        report.AppendLine($"验证时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine($"验证文件: {SelectedFilePath}");
        report.AppendLine($"验证结果: {(ValidationResult?.IsValid == true ? "通过" : "失败")}");
        
        if (!ValidationResult?.IsValid == true)
        {
            report.AppendLine($"失败原因: {ValidationResult?.ErrorMessage}");
        }

        report.AppendLine();
        report.AppendLine("许可证详细信息:");
        report.AppendLine("-" + new string('-', 30));

        foreach (var property in LicenseProperties)
        {
            report.AppendLine($"{property.Name}: {property.Value}");
        }

        return report.ToString();
    }
}

/// <summary>
/// 许可证属性显示类
/// </summary>
public class LicenseProperty
{
    public string Name { get; set; }
    public string Value { get; set; }

    public LicenseProperty(string name, string value)
    {
        Name = name;
        Value = value;
    }
}
