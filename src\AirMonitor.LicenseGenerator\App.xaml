﻿<Application
    x:Class="AirMonitor.LicenseGenerator.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:AirMonitor.LicenseGenerator">
    <Application.Resources>
        <!--  Syncfusion主题资源  -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--  Windows 11 Light主题  -->
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Button.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TextBox.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ComboBox.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/CheckBox.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/RadioButton.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/DatePicker.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TabControl.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/GroupBox.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Label.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ListBox.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ListView.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Menu.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ProgressBar.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/StatusBar.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/ToolBar.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/TreeView.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.Windows11Light.WPF;component/MSControl/Window.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  全局字体设置  -->
            <Style TargetType="{x:Type Control}">
                <Setter Property="FontFamily" Value="Microsoft YaHei" />
                <Setter Property="FontSize" Value="12" />
            </Style>

            <!--  全局窗口样式  -->
            <Style TargetType="{x:Type Window}">
                <Setter Property="FontFamily" Value="Microsoft YaHei" />
                <Setter Property="FontSize" Value="12" />
                <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.WindowBrushKey}}" />
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
