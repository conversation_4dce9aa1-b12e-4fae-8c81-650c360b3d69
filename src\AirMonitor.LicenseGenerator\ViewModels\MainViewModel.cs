using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Windows.Input;

namespace AirMonitor.LicenseGenerator.ViewModels;

/// <summary>
/// License注册机主窗口ViewModel
/// 负责管理主界面的导航和状态
/// </summary>
public partial class MainViewModel : ObservableObject
{
    private readonly ILogger<MainViewModel> _logger;

    [ObservableProperty]
    private string applicationTitle = "AirMonitor License注册机 v1.0";

    [ObservableProperty]
    private string statusMessage = "就绪";

    [ObservableProperty]
    private bool isLoading = false;

    [ObservableProperty]
    private int selectedTabIndex = 0;

    [ObservableProperty]
    private LicenseGeneratorViewModel licenseGeneratorViewModel;

    [ObservableProperty]
    private LicenseValidatorViewModel licenseValidatorViewModel;

    [ObservableProperty]
    private TemplateManagerViewModel templateManagerViewModel;

    public MainViewModel(
        ILogger<MainViewModel> logger,
        LicenseGeneratorViewModel licenseGeneratorViewModel,
        LicenseValidatorViewModel licenseValidatorViewModel,
        TemplateManagerViewModel templateManagerViewModel)
    {
        _logger = logger;
        this.licenseGeneratorViewModel = licenseGeneratorViewModel;
        this.licenseValidatorViewModel = licenseValidatorViewModel;
        this.templateManagerViewModel = templateManagerViewModel;

        _logger.LogInformation("License注册机主界面已初始化");
    }

    [RelayCommand]
    private void SwitchToGenerator()
    {
        SelectedTabIndex = 0;
        StatusMessage = "许可证生成器";
        _logger.LogDebug("切换到许可证生成器");
    }

    [RelayCommand]
    private void SwitchToValidator()
    {
        SelectedTabIndex = 1;
        StatusMessage = "许可证验证器";
        _logger.LogDebug("切换到许可证验证器");
    }

    [RelayCommand]
    private void SwitchToTemplateManager()
    {
        SelectedTabIndex = 2;
        StatusMessage = "模板管理器";
        _logger.LogDebug("切换到模板管理器");
    }

    [RelayCommand]
    private async Task ShowAboutAsync()
    {
        try
        {
            var aboutMessage = $"""
                {ApplicationTitle}
                
                版本: 1.0.0
                构建日期: {DateTime.Now:yyyy-MM-dd}
                
                功能特性:
                • 支持4种许可证类型生成
                • 硬件绑定和加密保护
                • 批量生成和模板管理
                • 许可证验证和信息查看
                
                技术架构:
                • WPF + .NET 8.0
                • Syncfusion Windows11主题
                • MVVM架构模式
                
                © 2024 AirMonitor Team
                """;

            // TODO: 使用Syncfusion MessageBox显示关于信息
            System.Windows.MessageBox.Show(aboutMessage, "关于", 
                System.Windows.MessageBoxButton.OK, 
                System.Windows.MessageBoxImage.Information);

            _logger.LogInformation("显示关于对话框");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示关于对话框时发生错误");
        }
    }

    [RelayCommand]
    private async Task ExitApplicationAsync()
    {
        try
        {
            _logger.LogInformation("用户请求退出应用程序");
            System.Windows.Application.Current.Shutdown();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "退出应用程序时发生错误");
        }
    }

    /// <summary>
    /// 更新状态消息
    /// </summary>
    /// <param name="message">状态消息</param>
    /// <param name="isLoading">是否显示加载状态</param>
    public void UpdateStatus(string message, bool isLoading = false)
    {
        StatusMessage = message;
        IsLoading = isLoading;
        _logger.LogDebug("状态更新: {Message}, 加载中: {IsLoading}", message, isLoading);
    }
}
