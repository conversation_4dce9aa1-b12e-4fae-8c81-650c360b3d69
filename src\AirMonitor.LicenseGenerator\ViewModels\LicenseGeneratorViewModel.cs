using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using AirMonitor.Core.Models;
using AirMonitor.Core.Enums;
using AirMonitor.Core.Interfaces;
using System.Collections.ObjectModel;
using Microsoft.Win32;

namespace AirMonitor.LicenseGenerator.ViewModels;

/// <summary>
/// License生成器ViewModel
/// 负责许可证生成相关的界面逻辑
/// </summary>
public partial class LicenseGeneratorViewModel : ObservableObject
{
    private readonly ILogger<LicenseGeneratorViewModel> _logger;
    private readonly ILicenseGeneratorService _licenseGeneratorService;
    private readonly IHardwareFingerprintService _hardwareFingerprintService;

    [ObservableProperty]
    private string licenseId = Guid.NewGuid().ToString("N")[..8].ToUpper();

    [ObservableProperty]
    private string productName = "AirMonitor";

    [ObservableProperty]
    private string productVersion = "1.0.0";

    [ObservableProperty]
    private LicenseType selectedLicenseType = LicenseType.Standard;

    [ObservableProperty]
    private string departmentName = "";

    [ObservableProperty]
    private string departmentEmail = "";

    [ObservableProperty]
    private DateTime validFrom = DateTime.Now;

    [ObservableProperty]
    private DateTime validTo = DateTime.Now.AddYears(1);

    [ObservableProperty]
    private bool isPermanent = false;

    [ObservableProperty]
    private string hardwareFingerprint = "";

    [ObservableProperty]
    private string notes = "";

    [ObservableProperty]
    private bool isGenerating = false;

    [ObservableProperty]
    private string generationStatus = "";

    public ObservableCollection<LicenseType> AvailableLicenseTypes { get; }
    public ObservableCollection<FeatureCode> SelectedFeatures { get; }
    public ObservableCollection<FeatureCode> AvailableFeatures { get; }

    public LicenseGeneratorViewModel(
        ILogger<LicenseGeneratorViewModel> logger,
        ILicenseGeneratorService licenseGeneratorService,
        IHardwareFingerprintService hardwareFingerprintService)
    {
        _logger = logger;
        _licenseGeneratorService = licenseGeneratorService;
        _hardwareFingerprintService = hardwareFingerprintService;

        AvailableLicenseTypes = new ObservableCollection<LicenseType>(Enum.GetValues<LicenseType>());
        SelectedFeatures = new ObservableCollection<FeatureCode>();
        AvailableFeatures = new ObservableCollection<FeatureCode>(Enum.GetValues<FeatureCode>());

        // 监听许可证类型变化，自动更新功能权限
        PropertyChanged += OnPropertyChanged;

        UpdateFeaturesForLicenseType();
        _logger.LogInformation("License生成器ViewModel已初始化");
    }

    private void OnPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(SelectedLicenseType))
        {
            UpdateFeaturesForLicenseType();
        }
        else if (e.PropertyName == nameof(IsPermanent))
        {
            if (IsPermanent)
            {
                ValidTo = DateTime.MaxValue;
            }
            else
            {
                ValidTo = DateTime.Now.AddYears(1);
            }
        }
    }

    private void UpdateFeaturesForLicenseType()
    {
        SelectedFeatures.Clear();

        // 根据许可证类型自动选择功能
        var features = SelectedLicenseType switch
        {
            LicenseType.Standard => new[]
            {
                FeatureCode.SerialCommunication,
                FeatureCode.DataCollection,
                FeatureCode.RealTimeMonitoring,
                FeatureCode.HistoryDataQuery,
                FeatureCode.AlarmManagement
            },
            LicenseType.AfterSales => new[]
            {
                FeatureCode.SerialCommunication,
                FeatureCode.DataCollection,
                FeatureCode.RealTimeMonitoring,
                FeatureCode.HistoryDataQuery,
                FeatureCode.DataExport,
                FeatureCode.AdvancedAnalysis,
                FeatureCode.DataPlayback,
                FeatureCode.AlarmManagement
            },
            LicenseType.Development => Enum.GetValues<FeatureCode>(),
            LicenseType.Management => Enum.GetValues<FeatureCode>(),
            _ => Array.Empty<FeatureCode>()
        };

        foreach (var feature in features)
        {
            SelectedFeatures.Add(feature);
        }

        _logger.LogDebug("已为许可证类型 {LicenseType} 更新功能权限", SelectedLicenseType);
    }

    [RelayCommand]
    private async Task GetLocalHardwareFingerprintAsync()
    {
        try
        {
            GenerationStatus = "正在获取本机硬件指纹...";
            IsGenerating = true;

            var fingerprint = await Task.Run(() => _hardwareFingerprintService.GetLocalFingerprint());
            HardwareFingerprint = fingerprint;
            GenerationStatus = "本机硬件指纹获取成功";

            _logger.LogInformation("成功获取本机硬件指纹: {Fingerprint}", fingerprint);
        }
        catch (Exception ex)
        {
            GenerationStatus = $"获取硬件指纹失败: {ex.Message}";
            _logger.LogError(ex, "获取本机硬件指纹时发生错误");
        }
        finally
        {
            IsGenerating = false;
        }
    }

    [RelayCommand]
    private void GenerateNewLicenseId()
    {
        LicenseId = Guid.NewGuid().ToString("N")[..8].ToUpper();
        _logger.LogDebug("生成新的许可证ID: {LicenseId}", LicenseId);
    }

    [RelayCommand]
    private async Task GenerateLicenseAsync()
    {
        try
        {
            if (!ValidateInput())
                return;

            GenerationStatus = "正在生成许可证...";
            IsGenerating = true;

            var licenseInfo = CreateLicenseInfo();
            var licenseContent = await Task.Run(() => _licenseGeneratorService.GenerateLicense(licenseInfo));

            // 选择保存位置
            var saveDialog = new SaveFileDialog
            {
                Title = "保存许可证文件",
                Filter = "License文件 (*.lic)|*.lic|所有文件 (*.*)|*.*",
                FileName = $"AirMonitor_{SelectedLicenseType}_{LicenseId}.lic"
            };

            if (saveDialog.ShowDialog() == true)
            {
                await File.WriteAllTextAsync(saveDialog.FileName, licenseContent);
                GenerationStatus = $"许可证已保存到: {saveDialog.FileName}";
                _logger.LogInformation("许可证生成成功: {FileName}", saveDialog.FileName);
            }
            else
            {
                GenerationStatus = "许可证生成已取消";
            }
        }
        catch (Exception ex)
        {
            GenerationStatus = $"生成许可证失败: {ex.Message}";
            _logger.LogError(ex, "生成许可证时发生错误");
        }
        finally
        {
            IsGenerating = false;
        }
    }

    private bool ValidateInput()
    {
        if (string.IsNullOrWhiteSpace(DepartmentName))
        {
            GenerationStatus = "请输入部门名称";
            return false;
        }

        if (string.IsNullOrWhiteSpace(DepartmentEmail))
        {
            GenerationStatus = "请输入部门邮箱";
            return false;
        }

        if (string.IsNullOrWhiteSpace(HardwareFingerprint))
        {
            GenerationStatus = "请获取或输入硬件指纹";
            return false;
        }

        if (!IsPermanent && ValidTo <= ValidFrom)
        {
            GenerationStatus = "有效期结束时间必须晚于开始时间";
            return false;
        }

        return true;
    }

    private LicenseInfo CreateLicenseInfo()
    {
        return new LicenseInfo
        {
            LicenseId = LicenseId,
            ProductName = ProductName,
            ProductVersion = ProductVersion,
            LicenseType = SelectedLicenseType,
            DepartmentName = DepartmentName,
            DepartmentEmail = DepartmentEmail,
            ValidFrom = ValidFrom,
            ValidTo = IsPermanent ? DateTime.MaxValue : ValidTo,
            HardwareFingerprint = HardwareFingerprint,
            AuthorizedFeatures = SelectedFeatures.ToList(),
            Notes = Notes,
            GeneratedAt = DateTime.Now,
            GeneratedBy = Environment.UserName
        };
    }

    [RelayCommand]
    private void ClearForm()
    {
        LicenseId = Guid.NewGuid().ToString("N")[..8].ToUpper();
        DepartmentName = "";
        DepartmentEmail = "";
        HardwareFingerprint = "";
        Notes = "";
        ValidFrom = DateTime.Now;
        ValidTo = DateTime.Now.AddYears(1);
        IsPermanent = false;
        SelectedLicenseType = LicenseType.Standard;
        GenerationStatus = "";

        _logger.LogDebug("表单已清空");
    }
}
