﻿using System.Windows;
using AirMonitor.Core.Interfaces;
using AirMonitor.LicenseGenerator.Services;
using AirMonitor.LicenseGenerator.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Syncfusion.SfSkinManager;

namespace AirMonitor.LicenseGenerator;

/// <summary>
/// License Generator应用程序主类
/// 配置依赖注入和应用程序启动
/// </summary>
public partial class App : Application
{
    private readonly IHost _host;

    public App()
    {
        // 配置Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.File("logs/license-generator-.log", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        // 配置依赖注入
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // 注册日志
                services.AddLogging(logging =>
                {
                    logging.ClearProviders();
                    logging.AddSerilog(dispose: true);
                });

                // 注册服务
                services.AddSingleton<ICryptoService, CryptoService>();
                services.AddSingleton<IHardwareFingerprintService, HardwareFingerprintService>();
                services.AddSingleton<ILicenseGeneratorService, LicenseGeneratorService>();

                // 注册视图模型
                services.AddSingleton<MainViewModel>();
                services.AddTransient<LicenseGeneratorViewModel>();
                services.AddTransient<LicenseValidatorViewModel>();
                services.AddTransient<TemplateManagerViewModel>();

                // 注册主窗口
                services.AddSingleton<MainWindow>();
            })
            .UseSerilog()
            .Build();
    }

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // 设置同步框架授权
        Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("YOUR_SYNCFUSION_KEY");

        // 应用Windows11Light主题
        SfSkinManager.ApplyStylesOnApplication = true;
        
        // 从DI容器中获取并显示主窗口
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.DataContext = _host.Services.GetRequiredService<MainViewModel>();
        mainWindow.Show();
    }

    protected override void OnExit(ExitEventArgs e)
    {
        // 清理资源
        _host.Dispose();
        Log.CloseAndFlush();
        
        base.OnExit(e);
    }
}

