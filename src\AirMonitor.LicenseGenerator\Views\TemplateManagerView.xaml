<UserControl x:Class="AirMonitor.LicenseGenerator.Views.TemplateManagerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
             mc:Ignorable="d" 
             FontFamily="Microsoft YaHei"
             FontSize="14"
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
        
        <Style x:Key="SectionBorderStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="#E1E4E8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Background" Value="White"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 工具栏区域 -->
        <Border Grid.Row="0" Style="{StaticResource SectionBorderStyle}" Margin="20,20,20,10">
            <StackPanel>
                <TextBlock Text="模板管理" Style="{StaticResource SectionHeaderStyle}"/>
                
                <!-- 新建模板区域 -->
                <Grid Margin="0,10,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="模板名称:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="1" Text="{Binding TemplateName}" Padding="8" Margin="0,0,10,0"/>
                    
                    <TextBlock Grid.Column="2" Text="描述:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox Grid.Column="3" Text="{Binding TemplateDescription}" Padding="8" Margin="0,0,10,0"/>
                    
                    <Button Grid.Column="4" Content="创建模板" Command="{Binding CreateTemplateCommand}" 
                            Padding="15,8" Background="#28A745" Foreground="White" BorderThickness="0"/>
                </Grid>
                
                <!-- 操作按钮区域 -->
                <StackPanel Orientation="Horizontal">
                    <Button Content="编辑" Command="{Binding EditTemplateCommand}" 
                            Padding="12,6" Margin="0,0,10,0" IsEnabled="{Binding SelectedTemplate, Converter={StaticResource NullToBooleanConverter}}"/>
                    <Button Content="删除" Command="{Binding DeleteTemplateCommand}" 
                            Padding="12,6" Margin="0,0,10,0" IsEnabled="{Binding SelectedTemplate, Converter={StaticResource NullToBooleanConverter}}"
                            Background="#DC3545" Foreground="White" BorderThickness="0"/>
                    <Button Content="导入" Command="{Binding ImportTemplateCommand}" 
                            Padding="12,6" Margin="0,0,10,0"/>
                    <Button Content="导出" Command="{Binding ExportTemplateCommand}" 
                            Padding="12,6" Margin="0,0,10,0" IsEnabled="{Binding SelectedTemplate, Converter={StaticResource NullToBooleanConverter}}"/>
                    <Button Content="应用" Command="{Binding ApplyTemplateCommand}" 
                            Padding="12,6" Margin="0,0,10,0" IsEnabled="{Binding SelectedTemplate, Converter={StaticResource NullToBooleanConverter}}"
                            Background="#0078D4" Foreground="White" BorderThickness="0"/>
                    <Button Content="刷新" Command="{Binding LoadTemplatesCommand}" 
                            Padding="12,6"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 模板列表区域 -->
        <Border Grid.Row="1" Style="{StaticResource SectionBorderStyle}" Margin="20,0,20,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="模板列表" Style="{StaticResource SectionHeaderStyle}"/>
                
                <!-- 模板数据网格 -->
                <syncfusion:SfDataGrid 
                    Grid.Row="1"
                    ItemsSource="{Binding Templates}"
                    SelectedItem="{Binding SelectedTemplate}"
                    AutoGenerateColumns="False"
                    HeaderRowHeight="40"
                    RowHeight="35"
                    GridLinesVisibility="Both"
                    SelectionMode="Single"
                    Margin="0,10">
                    
                    <syncfusion:SfDataGrid.Columns>
                        <syncfusion:GridTextColumn 
                            HeaderText="模板名称" 
                            MappingName="Name" 
                            Width="200"
                            TextAlignment="Left"/>
                        <syncfusion:GridTextColumn 
                            HeaderText="许可证类型" 
                            MappingName="LicenseType" 
                            Width="120"
                            TextAlignment="Center"/>
                        <syncfusion:GridTextColumn 
                            HeaderText="有效期(天)" 
                            MappingName="ValidityDays" 
                            Width="100"
                            TextAlignment="Center"/>
                        <syncfusion:GridTextColumn 
                            HeaderText="描述" 
                            MappingName="Description" 
                            Width="*"
                            TextAlignment="Left"
                            TextWrapping="Wrap"/>
                        <syncfusion:GridTextColumn 
                            HeaderText="创建时间" 
                            MappingName="CreatedAt" 
                            Width="150"
                            TextAlignment="Center"
                            DisplayBinding="{Binding CreatedAt, StringFormat=yyyy-MM-dd HH:mm}"/>
                        <syncfusion:GridTextColumn 
                            HeaderText="创建者" 
                            MappingName="CreatedBy" 
                            Width="100"
                            TextAlignment="Center"/>
                    </syncfusion:SfDataGrid.Columns>
                </syncfusion:SfDataGrid>

                <!-- 状态栏 -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,10,0,0">
                    <TextBlock Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
                    <syncfusion:SfBusyIndicator 
                        IsBusy="{Binding IsLoading}"
                        AnimationType="DoubleCircle"
                        Width="16" Height="16"
                        Margin="15,0,0,0"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    
                    <!-- 模板统计信息 -->
                    <TextBlock Text="{Binding Templates.Count, StringFormat='共 {0} 个模板'}" 
                               VerticalAlignment="Center" 
                               HorizontalAlignment="Right"
                               Margin="20,0,0,0"
                               Foreground="Gray"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 加载遮罩 -->
        <Grid Grid.Row="0" Grid.RowSpan="2" 
              Background="#80000000" 
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <syncfusion:SfBusyIndicator 
                    IsBusy="{Binding IsLoading}"
                    AnimationType="DoubleCircle"
                    Width="48" Height="48"
                    Foreground="White"/>
                <TextBlock Text="正在处理..." 
                           Foreground="White" 
                           FontSize="16" 
                           HorizontalAlignment="Center" 
                           Margin="0,10,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
