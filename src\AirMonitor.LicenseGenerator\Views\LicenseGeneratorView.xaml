<UserControl x:Class="AirMonitor.LicenseGenerator.Views.LicenseGeneratorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
             mc:Ignorable="d" 
             FontFamily="Microsoft YaHei"
             FontSize="14"
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
        
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
        
        <Style x:Key="InputStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#D0D7DE"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="SectionBorderStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="#E1E4E8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Background" Value="White"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
        <StackPanel>
            <!-- 基本信息区域 -->
            <Border Style="{StaticResource SectionBorderStyle}">
                <StackPanel>
                    <TextBlock Text="基本信息" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 许可证ID -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="许可证ID:" Style="{StaticResource LabelStyle}"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding LicenseId}" Style="{StaticResource InputStyle}" Margin="0,5"/>
                        <Button Grid.Row="0" Grid.Column="2" Content="重新生成" Command="{Binding GenerateNewLicenseIdCommand}" 
                                Padding="10,5" Margin="10,5,0,5"/>

                        <!-- 产品名称 -->
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="产品名称:" Style="{StaticResource LabelStyle}"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding ProductName}" Style="{StaticResource InputStyle}" 
                                 Margin="0,5" IsReadOnly="True" Background="#F6F8FA"/>

                        <!-- 产品版本 -->
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="产品版本:" Style="{StaticResource LabelStyle}"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding ProductVersion}" Style="{StaticResource InputStyle}" Margin="0,5"/>

                        <!-- 部门名称 -->
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="部门名称:" Style="{StaticResource LabelStyle}"/>
                        <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding DepartmentName}" Style="{StaticResource InputStyle}" Margin="0,5"/>

                        <!-- 部门邮箱 -->
                        <TextBlock Grid.Row="4" Grid.Column="0" Text="部门邮箱:" Style="{StaticResource LabelStyle}"/>
                        <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding DepartmentEmail}" Style="{StaticResource InputStyle}" Margin="0,5"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 许可证类型区域 -->
            <Border Style="{StaticResource SectionBorderStyle}">
                <StackPanel>
                    <TextBlock Text="许可证类型" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <RadioButton Content="普通版" IsChecked="{Binding SelectedLicenseType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=Standard}" 
                                     Margin="0,0,20,0" VerticalAlignment="Center"/>
                        <RadioButton Content="售后版" IsChecked="{Binding SelectedLicenseType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=AfterSales}" 
                                     Margin="0,0,20,0" VerticalAlignment="Center"/>
                        <RadioButton Content="研发版" IsChecked="{Binding SelectedLicenseType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=Development}" 
                                     Margin="0,0,20,0" VerticalAlignment="Center"/>
                        <RadioButton Content="管理版" IsChecked="{Binding SelectedLicenseType, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=Management}" 
                                     VerticalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- 有效期设置区域 -->
            <Border Style="{StaticResource SectionBorderStyle}">
                <StackPanel>
                    <TextBlock Text="有效期设置" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 生效时间 -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="生效时间:" Style="{StaticResource LabelStyle}"/>
                        <DatePicker Grid.Row="0" Grid.Column="1" SelectedDate="{Binding ValidFrom}" Margin="0,5,10,5"/>

                        <!-- 到期时间 -->
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="到期时间:" Style="{StaticResource LabelStyle}"/>
                        <DatePicker Grid.Row="0" Grid.Column="3" SelectedDate="{Binding ValidTo}" 
                                    IsEnabled="{Binding IsPermanent, Converter={StaticResource InverseBooleanConverter}}" Margin="0,5"/>

                        <!-- 永久有效 -->
                        <CheckBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="4" Content="永久有效" 
                                  IsChecked="{Binding IsPermanent}" Margin="0,10,0,0"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 硬件绑定区域 -->
            <Border Style="{StaticResource SectionBorderStyle}">
                <StackPanel>
                    <TextBlock Text="硬件绑定" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="硬件指纹:" Style="{StaticResource LabelStyle}"/>
                        <TextBox Grid.Column="1" Text="{Binding HardwareFingerprint}" Style="{StaticResource InputStyle}" 
                                 FontFamily="Consolas" FontSize="12" Margin="0,5"/>
                        <Button Grid.Column="2" Content="获取本机指纹" Command="{Binding GetLocalHardwareFingerprintCommand}" 
                                Padding="10,5" Margin="10,5,0,5"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 备注区域 -->
            <Border Style="{StaticResource SectionBorderStyle}">
                <StackPanel>
                    <TextBlock Text="备注信息" Style="{StaticResource SectionHeaderStyle}"/>
                    <TextBox Text="{Binding Notes}" AcceptsReturn="True" TextWrapping="Wrap" 
                             Height="80" Style="{StaticResource InputStyle}"/>
                </StackPanel>
            </Border>

            <!-- 操作按钮区域 -->
            <Border Style="{StaticResource SectionBorderStyle}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Content="生成许可证" Command="{Binding GenerateLicenseCommand}" 
                                Padding="20,10" Margin="0,0,15,0" FontWeight="SemiBold"
                                Background="#0078D4" Foreground="White" BorderThickness="0"/>
                        <Button Content="清空表单" Command="{Binding ClearFormCommand}" 
                                Padding="20,10" Margin="0,0,15,0"/>
                        <Button Content="批量生成" Padding="20,10" IsEnabled="False" ToolTip="功能开发中"/>
                    </StackPanel>
                    
                    <!-- 状态显示 -->
                    <TextBlock Text="{Binding GenerationStatus}" 
                               HorizontalAlignment="Center" Margin="0,15,0,0"
                               FontWeight="Medium" Foreground="#D73A49"
                               Visibility="{Binding GenerationStatus, Converter={StaticResource StringToVisibilityConverter}}"/>
                    
                    <!-- 加载指示器 -->
                    <syncfusion:SfBusyIndicator 
                        IsBusy="{Binding IsGenerating}"
                        AnimationType="DoubleCircle"
                        Width="32" Height="32"
                        HorizontalAlignment="Center"
                        Margin="0,10,0,0"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
