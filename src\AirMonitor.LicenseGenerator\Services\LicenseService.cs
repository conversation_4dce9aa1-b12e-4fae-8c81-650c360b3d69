using Microsoft.Extensions.Logging;
using AirMonitor.Core.Models;
using AirMonitor.Core.Interfaces;
using AirMonitor.Core.Utilities;
using System.Text.Json;

namespace AirMonitor.LicenseGenerator.Services;

/// <summary>
/// License服务实现
/// 负责许可证验证功能
/// </summary>
public class LicenseService : ILicenseService
{
    private readonly ILogger<LicenseService> _logger;
    private readonly ICryptoService _cryptoService;

    public LicenseService(
        ILogger<LicenseService> logger,
        ICryptoService cryptoService)
    {
        _logger = logger;
        _cryptoService = cryptoService;
    }

    public ValidationResult ValidateLicense(string licenseFilePath)
    {
        try
        {
            _logger.LogInformation("开始验证许可证文件: {FilePath}", licenseFilePath);

            if (string.IsNullOrWhiteSpace(licenseFilePath))
                return ValidationResult.Failure("许可证文件路径不能为空");

            if (!File.Exists(licenseFilePath))
                return ValidationResult.Failure("许可证文件不存在");

            // 1. 读取许可证文件
            var licenseFileContent = File.ReadAllText(licenseFilePath);
            if (string.IsNullOrWhiteSpace(licenseFileContent))
                return ValidationResult.Failure("许可证文件内容为空");

            // 2. 解析许可证文件结构
            var licenseFile = JsonHelper.Deserialize<LicenseFile>(licenseFileContent);
            if (licenseFile == null)
                return ValidationResult.Failure("许可证文件格式无效");

            // 3. 验证文件版本
            if (licenseFile.Version != "1.0")
                return ValidationResult.Failure($"不支持的许可证文件版本: {licenseFile.Version}");

            // 4. 解密许可证内容
            string licenseJson;
            try
            {
                licenseJson = _cryptoService.Decrypt(licenseFile.Content);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解密许可证内容失败");
                return ValidationResult.Failure("许可证文件已损坏或密钥不匹配");
            }

            // 5. 反序列化许可证对象
            var licenseInfo = JsonHelper.Deserialize<LicenseInfo>(licenseJson);
            if (licenseInfo == null)
                return ValidationResult.Failure("许可证数据格式无效");

            // 6. 验证数字签名
            if (!_cryptoService.VerifySignature(licenseJson, licenseFile.Signature))
            {
                _logger.LogWarning("许可证数字签名验证失败");
                return ValidationResult.Failure("许可证数字签名验证失败，文件可能被篡改");
            }

            // 7. 验证许可证有效期
            var now = DateTime.Now;
            if (now < licenseInfo.ValidFrom)
                return ValidationResult.Failure($"许可证尚未生效，生效时间: {licenseInfo.ValidFrom:yyyy-MM-dd HH:mm:ss}");

            if (licenseInfo.ValidTo != DateTime.MaxValue && now > licenseInfo.ValidTo)
                return ValidationResult.Failure($"许可证已过期，过期时间: {licenseInfo.ValidTo:yyyy-MM-dd HH:mm:ss}");

            // 8. 验证基本信息完整性
            if (string.IsNullOrWhiteSpace(licenseInfo.LicenseId))
                return ValidationResult.Failure("许可证ID无效");

            if (string.IsNullOrWhiteSpace(licenseInfo.ProductName))
                return ValidationResult.Failure("产品名称无效");

            if (licenseInfo.AuthorizedFeatures == null || !licenseInfo.AuthorizedFeatures.Any())
                return ValidationResult.Failure("许可证未授权任何功能");

            _logger.LogInformation("许可证验证成功: {LicenseId}", licenseInfo.LicenseId);
            return ValidationResult.Success(licenseInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证许可证时发生错误: {FilePath}", licenseFilePath);
            return ValidationResult.Failure($"验证过程中发生错误: {ex.Message}");
        }
    }

    public async Task<ValidationResult> ValidateLicenseAsync(string licenseFilePath)
    {
        return await Task.Run(() => ValidateLicense(licenseFilePath));
    }

    public bool IsFeatureAuthorized(LicenseInfo licenseInfo, Core.Enums.FeatureCode featureCode)
    {
        try
        {
            if (licenseInfo?.AuthorizedFeatures == null)
                return false;

            var isAuthorized = licenseInfo.AuthorizedFeatures.Contains(featureCode);
            _logger.LogDebug("功能授权检查: {FeatureCode} = {IsAuthorized}", featureCode, isAuthorized);
            
            return isAuthorized;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查功能授权时发生错误: {FeatureCode}", featureCode);
            return false;
        }
    }

    public bool IsLicenseValid(LicenseInfo licenseInfo)
    {
        try
        {
            if (licenseInfo == null)
                return false;

            var now = DateTime.Now;
            var isValid = now >= licenseInfo.ValidFrom && 
                         (licenseInfo.ValidTo == DateTime.MaxValue || now <= licenseInfo.ValidTo);

            _logger.LogDebug("许可证有效性检查: {LicenseId} = {IsValid}", licenseInfo.LicenseId, isValid);
            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查许可证有效性时发生错误");
            return false;
        }
    }

    public TimeSpan GetRemainingTime(LicenseInfo licenseInfo)
    {
        try
        {
            if (licenseInfo?.ValidTo == DateTime.MaxValue)
                return TimeSpan.MaxValue; // 永久有效

            var remaining = licenseInfo.ValidTo - DateTime.Now;
            return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算许可证剩余时间时发生错误");
            return TimeSpan.Zero;
        }
    }

    public List<Core.Enums.FeatureCode> GetAuthorizedFeatures(LicenseInfo licenseInfo)
    {
        try
        {
            return licenseInfo?.AuthorizedFeatures?.ToList() ?? new List<Core.Enums.FeatureCode>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取授权功能列表时发生错误");
            return new List<Core.Enums.FeatureCode>();
        }
    }

    public string GetLicenseDisplayInfo(LicenseInfo licenseInfo)
    {
        try
        {
            if (licenseInfo == null)
                return "无效许可证";

            var validityInfo = licenseInfo.ValidTo == DateTime.MaxValue 
                ? "永久有效" 
                : $"有效期至 {licenseInfo.ValidTo:yyyy-MM-dd}";

            return $"{GetLicenseTypeDisplayName(licenseInfo.LicenseType)} - {validityInfo}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成许可证显示信息时发生错误");
            return "许可证信息获取失败";
        }
    }

    private static string GetLicenseTypeDisplayName(Core.Enums.LicenseType licenseType)
    {
        return licenseType switch
        {
            Core.Enums.LicenseType.Standard => "普通版",
            Core.Enums.LicenseType.AfterSales => "售后版",
            Core.Enums.LicenseType.Development => "研发版",
            Core.Enums.LicenseType.Management => "管理版",
            _ => licenseType.ToString()
        };
    }

    /// <summary>
    /// 许可证文件结构
    /// </summary>
    private class LicenseFile
    {
        public string Version { get; set; } = "";
        public string Content { get; set; } = "";
        public string Signature { get; set; } = "";
        public DateTime GeneratedAt { get; set; }
        public string Generator { get; set; } = "";
    }
}
