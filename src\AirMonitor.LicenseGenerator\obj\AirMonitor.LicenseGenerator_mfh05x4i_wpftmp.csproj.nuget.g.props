﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;E:\DevExpress 23.2\Components\Offline Packages;e:\DevExpress 24.2\Components\Offline Packages;E:\Microsoft Visual Studio\Shared\NuGetPackages;D:\Syncfusion\Essential Studio\WPF\29.1.33\ToolboxNuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="E:\DevExpress 23.2\Components\Offline Packages\" />
    <SourceRoot Include="e:\DevExpress 24.2\Components\Offline Packages\" />
    <SourceRoot Include="E:\Microsoft Visual Studio\Shared\NuGetPackages\" />
    <SourceRoot Include="D:\Syncfusion\Essential Studio\WPF\29.1.33\ToolboxNuGetPackages\" />
  </ItemGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgSyncfusion_Shared_WPF Condition=" '$(PkgSyncfusion_Shared_WPF)' == '' ">C:\Users\<USER>\.nuget\packages\syncfusion.shared.wpf\29.2.9</PkgSyncfusion_Shared_WPF>
    <PkgSyncfusion_Tools_WPF Condition=" '$(PkgSyncfusion_Tools_WPF)' == '' ">C:\Users\<USER>\.nuget\packages\syncfusion.tools.wpf\29.2.9</PkgSyncfusion_Tools_WPF>
    <PkgSyncfusion_SfTextInputLayout_WPF Condition=" '$(PkgSyncfusion_SfTextInputLayout_WPF)' == '' ">C:\Users\<USER>\.nuget\packages\syncfusion.sftextinputlayout.wpf\29.2.9</PkgSyncfusion_SfTextInputLayout_WPF>
    <PkgSyncfusion_SfGrid_WPF Condition=" '$(PkgSyncfusion_SfGrid_WPF)' == '' ">C:\Users\<USER>\.nuget\packages\syncfusion.sfgrid.wpf\29.2.9</PkgSyncfusion_SfGrid_WPF>
  </PropertyGroup>
</Project>