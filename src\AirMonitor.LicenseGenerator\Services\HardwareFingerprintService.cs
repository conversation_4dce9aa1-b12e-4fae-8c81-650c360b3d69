using Microsoft.Extensions.Logging;
using AirMonitor.Core.Interfaces;
using AirMonitor.Core.Models;
using System.Management;
using System.Security.Cryptography;
using System.Text;

namespace AirMonitor.LicenseGenerator.Services;

/// <summary>
/// 硬件指纹服务实现
/// 负责获取和生成硬件指纹信息
/// </summary>
public class HardwareFingerprintService : IHardwareFingerprintService
{
    private readonly ILogger<HardwareFingerprintService> _logger;

    public HardwareFingerprintService(ILogger<HardwareFingerprintService> logger)
    {
        _logger = logger;
    }

    public string GetFingerprint()
    {
        try
        {
            _logger.LogDebug("开始获取本机硬件指纹");

            var fingerprintInfo = GetLocalHardwareInfo();
            var fingerprint = GenerateFingerprint(fingerprintInfo);

            _logger.LogInformation("本机硬件指纹获取成功: {Fingerprint}", fingerprint);
            return fingerprint;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取本机硬件指纹失败");
            throw;
        }
    }

    public async Task<string> GetFingerprintAsync()
    {
        return await Task.Run(() => GetFingerprint());
    }

    public HardwareFingerprintInfo GetLocalHardwareInfo()
    {
        try
        {
            _logger.LogDebug("开始获取本机硬件信息");

            var info = new HardwareFingerprintInfo
            {
                CpuId = GetCpuId(),
                MotherboardId = GetMotherboardId(),
                BiosId = GetBiosId(),
                MacAddress = GetMacAddress(),
                HardDiskId = GetHardDiskId(),
                ComputerName = Environment.MachineName,
                UserName = Environment.UserName,
                OsVersion = Environment.OSVersion.ToString(),
                CollectedAt = DateTime.Now
            };

            _logger.LogDebug("硬件信息获取完成: CPU={CpuId}, 主板={MotherboardId}", 
                info.CpuId?[..8], info.MotherboardId?[..8]);

            return info;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取硬件信息失败");
            throw;
        }
    }

    public string GenerateFingerprint(HardwareFingerprintInfo hardwareInfo)
    {
        try
        {
            if (hardwareInfo == null)
                throw new ArgumentNullException(nameof(hardwareInfo));

            // 组合关键硬件信息
            var components = new[]
            {
                hardwareInfo.CpuId ?? "",
                hardwareInfo.MotherboardId ?? "",
                hardwareInfo.BiosId ?? "",
                hardwareInfo.MacAddress ?? "",
                hardwareInfo.HardDiskId ?? ""
            };

            var combinedInfo = string.Join("|", components.Where(c => !string.IsNullOrEmpty(c)));
            
            if (string.IsNullOrEmpty(combinedInfo))
                throw new InvalidOperationException("无法获取足够的硬件信息生成指纹");

            // 生成SHA256哈希
            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combinedInfo));
            var fingerprint = Convert.ToHexString(hashBytes);

            _logger.LogDebug("硬件指纹生成完成: {Fingerprint}", fingerprint);
            return fingerprint;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成硬件指纹失败");
            throw;
        }
    }

    public bool ValidateFingerprint(string fingerprint, HardwareFingerprintInfo hardwareInfo)
    {
        try
        {
            if (string.IsNullOrEmpty(fingerprint))
                throw new ArgumentException("指纹不能为空", nameof(fingerprint));

            var currentFingerprint = GenerateFingerprint(hardwareInfo);
            var isValid = string.Equals(fingerprint, currentFingerprint, StringComparison.OrdinalIgnoreCase);

            _logger.LogDebug("硬件指纹验证结果: {IsValid}", isValid);
            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证硬件指纹失败");
            return false;
        }
    }

    private string GetCpuId()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor");
            using var collection = searcher.Get();
            
            foreach (ManagementObject obj in collection)
            {
                var cpuId = obj["ProcessorId"]?.ToString();
                if (!string.IsNullOrEmpty(cpuId))
                {
                    _logger.LogDebug("CPU ID获取成功");
                    return cpuId;
                }
            }

            throw new InvalidOperationException("无法获取CPU ID");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取CPU ID失败，使用备用方法");
            // 备用方法：使用环境信息
            return Environment.ProcessorCount.ToString() + Environment.MachineName;
        }
    }

    private string GetMotherboardId()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard");
            using var collection = searcher.Get();
            
            foreach (ManagementObject obj in collection)
            {
                var serialNumber = obj["SerialNumber"]?.ToString();
                if (!string.IsNullOrEmpty(serialNumber) && serialNumber != "To be filled by O.E.M.")
                {
                    _logger.LogDebug("主板序列号获取成功");
                    return serialNumber;
                }
            }

            // 尝试获取主板制造商和型号
            using var searcher2 = new ManagementObjectSearcher("SELECT Manufacturer, Product FROM Win32_BaseBoard");
            using var collection2 = searcher2.Get();
            
            foreach (ManagementObject obj in collection2)
            {
                var manufacturer = obj["Manufacturer"]?.ToString();
                var product = obj["Product"]?.ToString();
                if (!string.IsNullOrEmpty(manufacturer) && !string.IsNullOrEmpty(product))
                {
                    _logger.LogDebug("主板信息获取成功");
                    return $"{manufacturer}-{product}";
                }
            }

            throw new InvalidOperationException("无法获取主板信息");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取主板信息失败，使用备用方法");
            return Environment.MachineName + "-MOTHERBOARD";
        }
    }

    private string GetBiosId()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BIOS");
            using var collection = searcher.Get();
            
            foreach (ManagementObject obj in collection)
            {
                var serialNumber = obj["SerialNumber"]?.ToString();
                if (!string.IsNullOrEmpty(serialNumber))
                {
                    _logger.LogDebug("BIOS序列号获取成功");
                    return serialNumber;
                }
            }

            throw new InvalidOperationException("无法获取BIOS序列号");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取BIOS序列号失败");
            return "UNKNOWN-BIOS";
        }
    }

    private string GetMacAddress()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT MACAddress FROM Win32_NetworkAdapter WHERE MACAddress IS NOT NULL AND NetConnectionStatus = 2");
            using var collection = searcher.Get();
            
            foreach (ManagementObject obj in collection)
            {
                var macAddress = obj["MACAddress"]?.ToString();
                if (!string.IsNullOrEmpty(macAddress))
                {
                    _logger.LogDebug("MAC地址获取成功");
                    return macAddress.Replace(":", "").Replace("-", "");
                }
            }

            throw new InvalidOperationException("无法获取MAC地址");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取MAC地址失败");
            return "UNKNOWN-MAC";
        }
    }

    private string GetHardDiskId()
    {
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive WHERE MediaType = 'Fixed hard disk media'");
            using var collection = searcher.Get();
            
            foreach (ManagementObject obj in collection)
            {
                var serialNumber = obj["SerialNumber"]?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(serialNumber))
                {
                    _logger.LogDebug("硬盘序列号获取成功");
                    return serialNumber;
                }
            }

            throw new InvalidOperationException("无法获取硬盘序列号");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取硬盘序列号失败");
            return "UNKNOWN-DISK";
        }
    }

    public async Task<HardwareFingerprintInfo> GetDetailedFingerprintAsync()
    {
        return await Task.Run(() => GetLocalHardwareInfo());
    }

    public async Task<string> GetCpuIdAsync()
    {
        return await Task.Run(() => GetCpuId());
    }

    public async Task<string> GetMotherboardIdAsync()
    {
        return await Task.Run(() => GetMotherboardId());
    }

    public async Task<string> GetMacAddressAsync()
    {
        return await Task.Run(() => GetMacAddress());
    }

    public async Task<string> GetDiskIdAsync()
    {
        return await Task.Run(() => GetHardDiskId());
    }

    public string GetComputerName()
    {
        return Environment.MachineName;
    }

    public string GetUserName()
    {
        return Environment.UserName;
    }

    public async Task<string> GetOSInfoAsync()
    {
        return await Task.Run(() => Environment.OSVersion.ToString());
    }

    public async Task<bool> ValidateFingerprintAsync(string targetFingerprint)
    {
        return await Task.Run(() =>
        {
            var currentInfo = GetLocalHardwareInfo();
            return ValidateFingerprint(targetFingerprint, currentInfo);
        });
    }

    public async Task<string> GenerateFingerprintAsync(string cpuId, string motherboardId, string? macAddress = null)
    {
        return await Task.Run(() =>
        {
            var info = new HardwareFingerprintInfo
            {
                CpuId = cpuId,
                MotherboardId = motherboardId,
                MacAddress = macAddress ?? "",
                CollectedAt = DateTime.Now
            };
            return GenerateFingerprint(info);
        });
    }

    public async Task<HardwareFingerprintInfo?> GetRemoteFingerprintAsync(string targetMachine)
    {
        // TODO: 实现远程硬件指纹获取
        // 这可能需要在目标机器上运行一个小工具来收集硬件信息
        await Task.Delay(100); // 占位符
        throw new NotImplementedException("远程硬件指纹获取功能待实现");
    }

    public async Task<bool> ExportFingerprintAsync(string filePath, HardwareFingerprintInfo fingerprintInfo)
    {
        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(fingerprintInfo, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(filePath, json);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出硬件指纹失败: {FilePath}", filePath);
            return false;
        }
    }

    public async Task<HardwareFingerprintInfo?> ImportFingerprintAsync(string filePath)
    {
        try
        {
            var json = await File.ReadAllTextAsync(filePath);
            return System.Text.Json.JsonSerializer.Deserialize<HardwareFingerprintInfo>(json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导入硬件指纹失败: {FilePath}", filePath);
            return null;
        }
    }

    public async Task<HardwareChangeResult> DetectHardwareChangesAsync(string originalFingerprint)
    {
        return await Task.Run(() =>
        {
            var currentInfo = GetLocalHardwareInfo();
            var currentFingerprint = GenerateFingerprint(currentInfo);

            var result = new HardwareChangeResult
            {
                OriginalFingerprint = originalFingerprint,
                CurrentFingerprint = currentFingerprint,
                HasChanged = !string.Equals(originalFingerprint, currentFingerprint, StringComparison.OrdinalIgnoreCase),
                DetectedAt = DateTime.UtcNow
            };

            if (result.HasChanged)
            {
                result.ChangedComponents.Add("硬件配置已发生变化");
                result.Details = "检测到硬件环境变化，可能是硬件更换或系统重装导致";
            }

            return result;
        });
    }

    public string FormatFingerprint(string fingerprint)
    {
        if (string.IsNullOrEmpty(fingerprint))
            return "";

        // 格式化为 XXXX-XXXX-XXXX-XXXX 的形式
        if (fingerprint.Length >= 16)
        {
            return $"{fingerprint[..4]}-{fingerprint[4..8]}-{fingerprint[8..12]}-{fingerprint[12..16]}";
        }

        return fingerprint;
    }

    public bool IsValidFingerprintFormat(string fingerprint)
    {
        if (string.IsNullOrEmpty(fingerprint))
            return false;

        // 移除分隔符
        var cleanFingerprint = fingerprint.Replace("-", "").Replace(" ", "");
        
        // 检查是否为有效的十六进制字符串
        return cleanFingerprint.Length >= 16 && 
               cleanFingerprint.All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f'));
    }
}
