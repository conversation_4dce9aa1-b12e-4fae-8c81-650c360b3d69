using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using AirMonitor.Core.Models;
using AirMonitor.Core.Utilities;
using Microsoft.Win32;
using System.Collections.ObjectModel;

namespace AirMonitor.LicenseGenerator.ViewModels;

/// <summary>
/// 模板管理器ViewModel
/// 负责许可证模板的管理功能
/// </summary>
public partial class TemplateManagerViewModel : ObservableObject
{
    private readonly ILogger<TemplateManagerViewModel> _logger;
    private readonly string _templateDirectory;

    [ObservableProperty]
    private LicenseTemplate? selectedTemplate;

    [ObservableProperty]
    private bool isLoading = false;

    [ObservableProperty]
    private string statusMessage = "";

    [ObservableProperty]
    private string templateName = "";

    [ObservableProperty]
    private string templateDescription = "";

    public ObservableCollection<LicenseTemplate> Templates { get; }

    public TemplateManagerViewModel(ILogger<TemplateManagerViewModel> logger)
    {
        _logger = logger;
        Templates = new ObservableCollection<LicenseTemplate>();
        
        // 确保模板目录存在
        _templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Templates");
        Directory.CreateDirectory(_templateDirectory);

        LoadTemplatesAsync();
        _logger.LogInformation("模板管理器ViewModel已初始化");
    }

    [RelayCommand]
    private async Task LoadTemplatesAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在加载模板...";
            Templates.Clear();

            var templateFiles = Directory.GetFiles(_templateDirectory, "*.json");
            
            foreach (var file in templateFiles)
            {
                try
                {
                    var json = await File.ReadAllTextAsync(file);
                    var template = JsonHelper.Deserialize<LicenseTemplate>(json);
                    if (template != null)
                    {
                        template.FilePath = file;
                        Templates.Add(template);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "加载模板文件失败: {FilePath}", file);
                }
            }

            StatusMessage = $"已加载 {Templates.Count} 个模板";
            _logger.LogInformation("已加载 {Count} 个许可证模板", Templates.Count);
        }
        catch (Exception ex)
        {
            StatusMessage = $"加载模板失败: {ex.Message}";
            _logger.LogError(ex, "加载许可证模板时发生错误");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task CreateTemplateAsync()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(TemplateName))
            {
                StatusMessage = "请输入模板名称";
                return;
            }

            IsLoading = true;
            StatusMessage = "正在创建模板...";

            var template = new LicenseTemplate
            {
                Id = Guid.NewGuid().ToString(),
                Name = TemplateName,
                Description = TemplateDescription,
                LicenseType = Core.Enums.LicenseType.Standard,
                CreatedAt = DateTime.Now,
                CreatedBy = Environment.UserName,
                // 默认配置
                ProductName = "AirMonitor",
                ProductVersion = "1.0.0",
                ValidityDays = 365,
                AuthorizedFeatures = new List<Core.Enums.FeatureCode>
                {
                    Core.Enums.FeatureCode.SerialCommunication,
                    Core.Enums.FeatureCode.DataCollection,
                    Core.Enums.FeatureCode.RealTimeMonitoring
                }
            };

            await SaveTemplateAsync(template);
            Templates.Add(template);
            
            // 清空输入
            TemplateName = "";
            TemplateDescription = "";
            
            StatusMessage = $"模板 '{template.Name}' 创建成功";
            _logger.LogInformation("创建许可证模板成功: {TemplateName}", template.Name);
        }
        catch (Exception ex)
        {
            StatusMessage = $"创建模板失败: {ex.Message}";
            _logger.LogError(ex, "创建许可证模板时发生错误");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task EditTemplateAsync()
    {
        try
        {
            if (SelectedTemplate == null)
            {
                StatusMessage = "请选择要编辑的模板";
                return;
            }

            // TODO: 实现模板编辑对话框
            // 这里可以打开一个编辑对话框，让用户修改模板属性
            StatusMessage = "模板编辑功能待实现";
            _logger.LogDebug("请求编辑模板: {TemplateName}", SelectedTemplate.Name);
        }
        catch (Exception ex)
        {
            StatusMessage = $"编辑模板失败: {ex.Message}";
            _logger.LogError(ex, "编辑许可证模板时发生错误");
        }
    }

    [RelayCommand]
    private async Task DeleteTemplateAsync()
    {
        try
        {
            if (SelectedTemplate == null)
            {
                StatusMessage = "请选择要删除的模板";
                return;
            }

            var result = System.Windows.MessageBox.Show(
                $"确定要删除模板 '{SelectedTemplate.Name}' 吗？此操作不可撤销。",
                "确认删除",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Question);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                IsLoading = true;
                StatusMessage = "正在删除模板...";

                if (!string.IsNullOrEmpty(SelectedTemplate.FilePath) && File.Exists(SelectedTemplate.FilePath))
                {
                    File.Delete(SelectedTemplate.FilePath);
                }

                Templates.Remove(SelectedTemplate);
                var templateName = SelectedTemplate.Name;
                SelectedTemplate = null;

                StatusMessage = $"模板 '{templateName}' 已删除";
                _logger.LogInformation("删除许可证模板成功: {TemplateName}", templateName);
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"删除模板失败: {ex.Message}";
            _logger.LogError(ex, "删除许可证模板时发生错误");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task ImportTemplateAsync()
    {
        try
        {
            var openDialog = new OpenFileDialog
            {
                Title = "导入许可证模板",
                Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                Multiselect = true
            };

            if (openDialog.ShowDialog() == true)
            {
                IsLoading = true;
                StatusMessage = "正在导入模板...";

                int importedCount = 0;
                foreach (var filePath in openDialog.FileNames)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(filePath);
                        var template = JsonHelper.Deserialize<LicenseTemplate>(json);
                        
                        if (template != null)
                        {
                            // 生成新的ID和文件路径
                            template.Id = Guid.NewGuid().ToString();
                            await SaveTemplateAsync(template);
                            Templates.Add(template);
                            importedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "导入模板文件失败: {FilePath}", filePath);
                    }
                }

                StatusMessage = $"成功导入 {importedCount} 个模板";
                _logger.LogInformation("导入许可证模板完成，成功导入 {Count} 个", importedCount);
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"导入模板失败: {ex.Message}";
            _logger.LogError(ex, "导入许可证模板时发生错误");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task ExportTemplateAsync()
    {
        try
        {
            if (SelectedTemplate == null)
            {
                StatusMessage = "请选择要导出的模板";
                return;
            }

            var saveDialog = new SaveFileDialog
            {
                Title = "导出许可证模板",
                Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                FileName = $"{SelectedTemplate.Name}.json"
            };

            if (saveDialog.ShowDialog() == true)
            {
                IsLoading = true;
                StatusMessage = "正在导出模板...";

                var json = JsonHelper.Serialize(SelectedTemplate, true);
                await File.WriteAllTextAsync(saveDialog.FileName, json);

                StatusMessage = $"模板已导出到: {saveDialog.FileName}";
                _logger.LogInformation("导出许可证模板成功: {FileName}", saveDialog.FileName);
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"导出模板失败: {ex.Message}";
            _logger.LogError(ex, "导出许可证模板时发生错误");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task ApplyTemplateAsync()
    {
        try
        {
            if (SelectedTemplate == null)
            {
                StatusMessage = "请选择要应用的模板";
                return;
            }

            // TODO: 实现模板应用功能
            // 将选中的模板应用到许可证生成器
            StatusMessage = "模板应用功能待实现";
            _logger.LogDebug("请求应用模板: {TemplateName}", SelectedTemplate.Name);
        }
        catch (Exception ex)
        {
            StatusMessage = $"应用模板失败: {ex.Message}";
            _logger.LogError(ex, "应用许可证模板时发生错误");
        }
    }

    private async Task SaveTemplateAsync(LicenseTemplate template)
    {
        var fileName = $"{template.Name}_{template.Id}.json";
        var filePath = Path.Combine(_templateDirectory, fileName);
        
        var json = JsonHelper.Serialize(template, true);
        await File.WriteAllTextAsync(filePath, json);
        
        template.FilePath = filePath;
    }
}
