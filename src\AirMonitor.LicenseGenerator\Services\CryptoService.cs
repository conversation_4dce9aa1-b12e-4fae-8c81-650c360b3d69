using Microsoft.Extensions.Logging;
using AirMonitor.Core.Interfaces;
using AirMonitor.Core.Models;
using AirMonitor.Core.Constants;
using System.Security.Cryptography;
using System.Text;

namespace AirMonitor.LicenseGenerator.Services;

/// <summary>
/// 加密服务实现
/// 提供RSA+AES混合加密和数字签名功能
/// </summary>
public class CryptoService : ICryptoService
{
    private readonly ILogger<CryptoService> _logger;
    private readonly RSA _rsa;
    private readonly string _privateKeyPath;
    private readonly string _publicKeyPath;

    public CryptoService(ILogger<CryptoService> logger)
    {
        _logger = logger;
        _rsa = RSA.Create(CryptoConstants.RSAKeySize);

        // 密钥文件路径
        var keyDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Keys");
        Directory.CreateDirectory(keyDirectory);

        _privateKeyPath = Path.Combine(keyDirectory, "private.key");
        _publicKeyPath = Path.Combine(keyDirectory, "public.key");

        InitializeKeys();
        _logger.LogInformation("加密服务已初始化，RSA密钥大小: {KeySize}", CryptoConstants.RSAKeySize);
    }

    public string Encrypt(string plainText)
    {
        try
        {
            if (string.IsNullOrEmpty(plainText))
                throw new ArgumentException("明文不能为空", nameof(plainText));

            // 1. 生成随机AES密钥
            using var aes = Aes.Create();
            aes.KeySize = CryptoConstants.AESKeySize;
            aes.GenerateKey();
            aes.GenerateIV();

            // 2. 使用AES加密数据
            var plainBytes = Encoding.UTF8.GetBytes(plainText);
            byte[] encryptedData;
            
            using (var encryptor = aes.CreateEncryptor())
            using (var msEncrypt = new MemoryStream())
            using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
            {
                csEncrypt.Write(plainBytes, 0, plainBytes.Length);
                csEncrypt.FlushFinalBlock();
                encryptedData = msEncrypt.ToArray();
            }

            // 3. 使用RSA加密AES密钥
            var encryptedKey = _rsa.Encrypt(aes.Key, RSAEncryptionPadding.OaepSHA256);
            var encryptedIV = _rsa.Encrypt(aes.IV, RSAEncryptionPadding.OaepSHA256);

            // 4. 组合加密结果
            var result = new
            {
                EncryptedKey = Convert.ToBase64String(encryptedKey),
                EncryptedIV = Convert.ToBase64String(encryptedIV),
                EncryptedData = Convert.ToBase64String(encryptedData)
            };

            var resultJson = System.Text.Json.JsonSerializer.Serialize(result);
            var finalResult = Convert.ToBase64String(Encoding.UTF8.GetBytes(resultJson));

            _logger.LogDebug("数据加密完成，原始大小: {PlainSize}, 加密后大小: {EncryptedSize}", 
                plainText.Length, finalResult.Length);

            return finalResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据加密失败");
            throw;
        }
    }

    public string Decrypt(string encryptedText)
    {
        try
        {
            if (string.IsNullOrEmpty(encryptedText))
                throw new ArgumentException("密文不能为空", nameof(encryptedText));

            // 1. 解析加密数据
            var encryptedBytes = Convert.FromBase64String(encryptedText);
            var encryptedJson = Encoding.UTF8.GetString(encryptedBytes);
            var encryptedData = System.Text.Json.JsonSerializer.Deserialize<EncryptedDataContainer>(encryptedJson);

            if (encryptedData == null)
                throw new InvalidOperationException("无法解析加密数据");

            // 2. 使用RSA解密AES密钥
            var aesKey = _rsa.Decrypt(Convert.FromBase64String(encryptedData.EncryptedKey), RSAEncryptionPadding.OaepSHA256);
            var aesIV = _rsa.Decrypt(Convert.FromBase64String(encryptedData.EncryptedIV), RSAEncryptionPadding.OaepSHA256);

            // 3. 使用AES解密数据
            using var aes = Aes.Create();
            aes.Key = aesKey;
            aes.IV = aesIV;

            var cipherBytes = Convert.FromBase64String(encryptedData.EncryptedData);
            
            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(cipherBytes);
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var srDecrypt = new StreamReader(csDecrypt);
            
            var plainText = srDecrypt.ReadToEnd();

            _logger.LogDebug("数据解密完成，解密后大小: {PlainSize}", plainText.Length);
            return plainText;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据解密失败");
            throw;
        }
    }

    public string GenerateSignature(string data)
    {
        try
        {
            if (string.IsNullOrEmpty(data))
                throw new ArgumentException("数据不能为空", nameof(data));

            var dataBytes = Encoding.UTF8.GetBytes(data);
            var signature = _rsa.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            var result = Convert.ToBase64String(signature);

            _logger.LogDebug("数字签名生成完成，数据大小: {DataSize}, 签名大小: {SignatureSize}", 
                data.Length, result.Length);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成数字签名失败");
            throw;
        }
    }

    public bool VerifySignature(string data, string signature)
    {
        try
        {
            if (string.IsNullOrEmpty(data))
                throw new ArgumentException("数据不能为空", nameof(data));

            if (string.IsNullOrEmpty(signature))
                throw new ArgumentException("签名不能为空", nameof(signature));

            var dataBytes = Encoding.UTF8.GetBytes(data);
            var signatureBytes = Convert.FromBase64String(signature);

            var isValid = _rsa.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);

            _logger.LogDebug("数字签名验证完成，结果: {IsValid}", isValid);
            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证数字签名失败");
            return false;
        }
    }

    public string GenerateHash(string data)
    {
        try
        {
            if (string.IsNullOrEmpty(data))
                throw new ArgumentException("数据不能为空", nameof(data));

            using var sha256 = SHA256.Create();
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var hashBytes = sha256.ComputeHash(dataBytes);
            var result = Convert.ToBase64String(hashBytes);

            _logger.LogDebug("哈希值生成完成，数据大小: {DataSize}, 哈希大小: {HashSize}", 
                data.Length, result.Length);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成哈希值失败");
            throw;
        }
    }

    public string GetPublicKey()
    {
        try
        {
            var publicKey = _rsa.ExportRSAPublicKeyPem();
            _logger.LogDebug("导出公钥完成");
            return publicKey;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出公钥失败");
            throw;
        }
    }

    private void InitializeKeys()
    {
        try
        {
            if (File.Exists(_privateKeyPath) && File.Exists(_publicKeyPath))
            {
                // 加载现有密钥
                var privateKeyPem = File.ReadAllText(_privateKeyPath);
                _rsa.ImportFromPem(privateKeyPem);
                _logger.LogInformation("已加载现有RSA密钥对");
            }
            else
            {
                // 生成新密钥对
                GenerateNewKeyPair();
                _logger.LogInformation("已生成新的RSA密钥对");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化RSA密钥失败");
            throw;
        }
    }

    private void GenerateNewKeyPair()
    {
        try
        {
            // 导出密钥
            var privateKeyPem = _rsa.ExportRSAPrivateKeyPem();
            var publicKeyPem = _rsa.ExportRSAPublicKeyPem();

            // 保存密钥文件
            File.WriteAllText(_privateKeyPath, privateKeyPem);
            File.WriteAllText(_publicKeyPath, publicKeyPem);

            // 设置文件权限（仅Windows）
            if (OperatingSystem.IsWindows())
            {
                var fileInfo = new FileInfo(_privateKeyPath);
                fileInfo.Attributes |= FileAttributes.Hidden;
            }

            _logger.LogInformation("RSA密钥对已生成并保存到: {PrivateKeyPath}, {PublicKeyPath}", 
                _privateKeyPath, _publicKeyPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成RSA密钥对失败");
            throw;
        }
    }

    public void Dispose()
    {
        _rsa?.Dispose();
        _logger.LogDebug("加密服务已释放资源");
    }

    /// <summary>
    /// 加密数据结构
    /// </summary>
    private class EncryptedDataContainer
    {
        public string EncryptedKey { get; set; } = "";
        public string EncryptedIV { get; set; } = "";
        public string EncryptedData { get; set; } = "";
    }
}
