﻿<Window
    x:Class="AirMonitor.LicenseGenerator.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:AirMonitor.LicenseGenerator"
    xmlns:views="clr-namespace:AirMonitor.LicenseGenerator.Views"
    xmlns:viewmodels="clr-namespace:AirMonitor.LicenseGenerator.ViewModels"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
    Title="{Binding ApplicationTitle}"
    Width="1200"
    Height="800"
    MinWidth="800"
    MinHeight="600"
    Icon="Resources/Images/license-generator.ico"
    WindowStartupLocation="CenterScreen"
    FontFamily="Microsoft YaHei"
    FontSize="14"
    mc:Ignorable="d">

    <Window.Resources>
        <!-- 统一的样式资源 -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="SectionBorderStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <!-- DataTemplates for ViewModels -->
        <DataTemplate DataType="{x:Type viewmodels:LicenseGeneratorViewModel}">
            <views:LicenseGeneratorView/>
        </DataTemplate>

        <DataTemplate DataType="{x:Type viewmodels:LicenseValidatorViewModel}">
            <views:LicenseValidatorView/>
        </DataTemplate>

        <DataTemplate DataType="{x:Type viewmodels:TemplateManagerViewModel}">
            <views:TemplateManagerView/>
        </DataTemplate>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="#F8F9FA" Padding="5">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="退出(_X)" Command="{Binding ExitApplicationCommand}"/>
            </MenuItem>
            <MenuItem Header="工具(_T)">
                <MenuItem Header="生成器" Command="{Binding SwitchToGeneratorCommand}"/>
                <MenuItem Header="验证器" Command="{Binding SwitchToValidatorCommand}"/>
                <MenuItem Header="模板管理" Command="{Binding SwitchToTemplateManagerCommand}"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="关于(_A)" Command="{Binding ShowAboutCommand}"/>
            </MenuItem>
        </Menu>

        <!-- 主工作区 -->
        <syncfusion:TabControlExt
            Grid.Row="1"
            SelectedIndex="{Binding SelectedTabIndex}"
            TabStripPlacement="Top"
            CloseButtonType="Hide"
            Margin="10">

            <!-- 许可证生成器 -->
            <syncfusion:TabItemExt Header="许可证生成器" Name="GeneratorTab">
                <ContentControl Content="{Binding LicenseGeneratorViewModel}"/>
            </syncfusion:TabItemExt>

            <!-- 许可证验证器 -->
            <syncfusion:TabItemExt Header="许可证验证器" Name="ValidatorTab">
                <ContentControl Content="{Binding LicenseValidatorViewModel}"/>
            </syncfusion:TabItemExt>

            <!-- 模板管理器 -->
            <syncfusion:TabItemExt Header="模板管理器" Name="TemplateManagerTab">
                <ContentControl Content="{Binding TemplateManagerViewModel}"/>
            </syncfusion:TabItemExt>
        </syncfusion:TabControlExt>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Background="#F8F9FA" Height="30">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="{Binding StatusMessage}" Margin="5,0"/>
                    <syncfusion:SfBusyIndicator
                        AnimationType="DoubleCircle"
                        IsBusy="{Binding IsLoading}"
                        Width="16" Height="16"
                        Margin="10,0,0,0"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding ApplicationTitle}" FontSize="12" Foreground="Gray"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
